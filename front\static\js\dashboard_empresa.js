/**
 * Dashboard Empresa - Auditoria Fiscal
 * Dashboard específico para uma empresa com cards por tributo
 */

// Variáveis globais
let empresaDashboardData = null;
let empresaId = null;

/**
 * Inicializa o dashboard da empresa
 */
function initDashboardEmpresa() {
  console.log('Inicializando dashboard da empresa...');

  // Extrair ID da empresa da URL
  const pathParts = window.location.pathname.split('/');
  if (
    pathParts.length >= 4 &&
    pathParts[1] === 'dashboard' &&
    pathParts[2] === 'empresa'
  ) {
    empresaId = parseInt(pathParts[3]);
    console.log('ID da empresa extraído da URL:', empresaId);

    // Adicionar classe para ocultar elementos do dashboard geral
    document.body.classList.add('dashboard-empresa-page');

    // Carregar dados do dashboard da empresa
    carregarDashboardEmpresa();

    // Configurar event listeners
    setupFiltrosDashboardEmpresa();
    setupBreadcrumb();
  } else {
    console.log('Não é uma página de dashboard de empresa');
    // Remover classe se não estivermos na página de empresa
    document.body.classList.remove('dashboard-empresa-page');
  }
}

/**
 * Configura o breadcrumb de navegação
 */
function setupBreadcrumb() {
  // Adicionar breadcrumb se não existir
  const breadcrumbContainer = document.querySelector('.breadcrumb-container');
  if (!breadcrumbContainer) {
    const header = document.querySelector('.section-header');
    if (header) {
      const breadcrumb = document.createElement('nav');
      breadcrumb.className = 'breadcrumb-container mb-3';
      breadcrumb.innerHTML = `
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="/dashboard" class="text-decoration-none">
                            <i class="fas fa-home"></i> Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item active" id="breadcrumb-empresa">
                        <i class="fas fa-building"></i> Carregando...
                    </li>
                </ol>
            `;
      header.appendChild(breadcrumb);
    }
  }
}

/**
 * Configura os filtros do dashboard da empresa
 */
function setupFiltrosDashboardEmpresa() {
  // Event listener para mudança de ano
  const yearSelect = document.getElementById('year-select');
  if (yearSelect) {
    yearSelect.addEventListener('change', function () {
      carregarDashboardEmpresa();
    });
  }

  // Event listener para mudança de mês
  const monthSelect = document.getElementById('month-select');
  if (monthSelect) {
    monthSelect.addEventListener('change', function () {
      carregarDashboardEmpresa();
    });
  }
}

/**
 * Carrega os dados do dashboard da empresa
 */
function carregarDashboardEmpresa() {
  if (!empresaId) {
    console.error('ID da empresa não definido');
    return;
  }

  const year =
    document.getElementById('year-select')?.value || new Date().getFullYear();
  const month =
    document.getElementById('month-select')?.value || new Date().getMonth() + 1;

  console.log('Carregando dashboard da empresa...', { empresaId, year, month });

  // Mostrar loading
  mostrarLoadingDashboardEmpresa();

  // Fazer requisição para a API
  fetch(`/api/dashboard/empresa/${empresaId}?year=${year}&month=${month}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      console.log('Dashboard da empresa recebido:', data);

      if (data.success) {
        empresaDashboardData = data;
        atualizarDashboardEmpresa(data);
      } else {
        console.error('Erro ao carregar dashboard da empresa:', data.message);
        mostrarErroDashboardEmpresa(data.message);
      }
    })
    .catch((error) => {
      console.error('Erro na requisição do dashboard da empresa:', error);
      mostrarErroDashboardEmpresa('Erro de conexão');
    });
}

/**
 * Atualiza o dashboard da empresa com os dados recebidos
 */
function atualizarDashboardEmpresa(data) {
  // Atualizar breadcrumb
  const breadcrumbEmpresa = document.getElementById('breadcrumb-empresa');
  if (breadcrumbEmpresa) {
    breadcrumbEmpresa.innerHTML = `<i class="fas fa-building"></i> ${data.empresa.razao_social}`;
  }

  // Atualizar título da página
  const pageTitle = document.querySelector('.section-header h2');
  if (pageTitle) {
    pageTitle.innerHTML = `<i class="fas fa-chart-line"></i> Dashboard - ${data.empresa.razao_social}`;
  }

  // Criar container para os cards se não existir
  let cardsContainer = document.getElementById('dashboard-empresa-cards');
  if (!cardsContainer) {
    const pageSection = document.querySelector('.page-section.active');
    if (pageSection) {
      cardsContainer = document.createElement('div');
      cardsContainer.id = 'dashboard-empresa-cards';
      cardsContainer.className = 'dashboard-empresa-cards';
      pageSection.appendChild(cardsContainer);
    }
  }

  if (cardsContainer) {
    // Gerar cards dos tributos
    gerarCardsTributos(cardsContainer, data.cards_tributos);

    // Gerar resumo geral
    gerarResumoGeral(cardsContainer, data.total_geral);
  }

  console.log('Dashboard da empresa atualizado com sucesso');
}

/**
 * Gera os cards dos tributos
 */
function gerarCardsTributos(container, cardsTributos) {
  let html = `
        <div class="row mb-4">
            <div class="col-12">
                <h4><i class="fas fa-calculator"></i> Tributos</h4>
                <p class="text-muted">Clique em um card para acessar a auditoria específica do tributo</p>
            </div>
        </div>
        <div class="row">
    `;

  cardsTributos.forEach((card) => {
    const statusClass = card.auditado ? 'border-success' : 'border-warning';
    const statusIcon = card.auditado
      ? 'fas fa-check-circle text-success'
      : 'fas fa-clock text-warning';
    const statusText = card.auditado ? 'Auditado' : 'Pendente';

    html += `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card tributo-card ${statusClass}"
                     onclick="navegarParaAuditoriaTributo('${
                       card.tipo_tributo
                     }')"
                     style="cursor: pointer; transition: transform 0.2s;">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="card-title mb-0">${
                              card.nome_tributo
                            }</h5>
                            <i class="${statusIcon}"></i>
                        </div>

                        <div class="mb-3">
                            <span class="badge ${
                              card.auditado ? 'bg-success' : 'bg-warning'
                            }">${statusText}</span>
                        </div>

                        ${
                          card.auditado
                            ? `
                            <div class="row text-center mb-3">
                                <div class="col-6">
                                    <small class="text-muted">Notas</small>
                                    <div class="fw-bold">${
                                      card.total_notas
                                    }</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Itens</small>
                                    <div class="fw-bold">${
                                      card.total_produtos
                                    }</div>
                                </div>
                            </div>

                            <div class="row text-center mb-3">
                                <div class="col-6">
                                    <small class="text-success">Conformes</small>
                                    <div class="fw-bold text-success">${
                                      card.total_conforme
                                    }</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-danger">Inconsistentes</small>
                                    <div class="fw-bold text-danger">${
                                      card.total_inconsistente
                                    }</div>
                                </div>
                            </div>

                            ${
                              card.total_inconsistente > 0
                                ? `
                                <div class="text-center">
                                    <small class="text-muted">Valor Inconsistente</small>
                                    <div class="fw-bold text-danger">
                                        ${formatCurrency(
                                          card.valor_inconsistente_maior +
                                            card.valor_inconsistente_menor,
                                        )}
                                    </div>
                                </div>
                            `
                                : ''
                            }
                        `
                            : `
                            <div class="text-center py-3">
                                <i class="fas fa-exclamation-triangle text-warning fa-2x mb-2"></i>
                                <p class="text-muted mb-0">Auditoria não realizada</p>
                            </div>
                        `
                        }
                    </div>
                </div>
            </div>
        `;
  });

  html += '</div>';

  // Adicionar ao container
  const existingCards = container.querySelector('.tributos-cards');
  if (existingCards) {
    existingCards.innerHTML = html;
  } else {
    const div = document.createElement('div');
    div.className = 'tributos-cards';
    div.innerHTML = html;
    container.appendChild(div);
  }
}

/**
 * Gera o resumo geral
 */
function gerarResumoGeral(container, totalGeral) {
  const html = `
        <div class="row mt-5">
            <div class="col-12">
                <h4><i class="fas fa-chart-bar"></i> Resumo Geral</h4>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h5>Tributos Auditados</h5>
                        <h2>${totalGeral.tributos_auditados}/6</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h5>Total de Notas</h5>
                        <h2>${totalGeral.total_notas}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h5>Itens Conformes</h5>
                        <h2>${totalGeral.total_conforme}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h5>Itens Inconsistentes</h5>
                        <h2>${totalGeral.total_inconsistente}</h2>
                    </div>
                </div>
            </div>
        </div>
    `;

  // Adicionar ao container
  const existingResumo = container.querySelector('.resumo-geral');
  if (existingResumo) {
    existingResumo.innerHTML = html;
  } else {
    const div = document.createElement('div');
    div.className = 'resumo-geral';
    div.innerHTML = html;
    container.appendChild(div);
  }
}

/**
 * Navega para a auditoria específica do tributo
 */
function navegarParaAuditoriaTributo(tipoTributo) {
  console.log('Navegando para auditoria do tributo:', tipoTributo);

  // Determinar direção (entrada/saída) - por padrão usar entrada
  const direcao = 'entrada';

  // Ajustar nome do tributo para URL
  const tributoUrl = tipoTributo.replace('_', '-');

  // Navegar para a página de auditoria
  window.location.href = `/auditoria/${direcao}/${tributoUrl}`;
}

/**
 * Funções auxiliares
 */
function formatCurrency(value) {
  if (!value || value === 0) return 'R$ 0,00';
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
}

/**
 * Funções de loading e erro
 */
function mostrarLoadingDashboardEmpresa() {
  const pageSection = document.querySelector('.page-section.active');
  if (pageSection) {
    let cardsContainer = document.getElementById('dashboard-empresa-cards');
    if (!cardsContainer) {
      cardsContainer = document.createElement('div');
      cardsContainer.id = 'dashboard-empresa-cards';
      cardsContainer.className = 'dashboard-empresa-cards';
      pageSection.appendChild(cardsContainer);
    }

    cardsContainer.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-spinner fa-spin fa-3x mb-3"></i>
                <h4>Carregando dashboard da empresa...</h4>
            </div>
        `;
  }
}

function mostrarErroDashboardEmpresa(message) {
  const pageSection = document.querySelector('.page-section.active');
  if (pageSection) {
    let cardsContainer = document.getElementById('dashboard-empresa-cards');
    if (!cardsContainer) {
      cardsContainer = document.createElement('div');
      cardsContainer.id = 'dashboard-empresa-cards';
      cardsContainer.className = 'dashboard-empresa-cards';
      pageSection.appendChild(cardsContainer);
    }

    cardsContainer.innerHTML = `
            <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <h4>Erro ao carregar dashboard</h4>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="carregarDashboardEmpresa()">
                    <i class="fas fa-redo"></i> Tentar Novamente
                </button>
            </div>
        `;
  }
}

// Inicializar quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function () {
  // Aguardar um pouco para garantir que outros scripts foram carregados
  setTimeout(initDashboardEmpresa, 500);
});

// Exportar funções para uso global
window.initDashboardEmpresa = initDashboardEmpresa;
window.navegarParaAuditoriaTributo = navegarParaAuditoriaTributo;
