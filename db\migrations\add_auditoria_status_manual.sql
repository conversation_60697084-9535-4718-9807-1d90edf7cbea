-- Migração: Ad<PERSON><PERSON>r tabela para controle manual de status de auditoria
-- Data: 2025-01-27
-- Descrição: Permite marcar tributos como "não aplicável" manualmente

CREATE TABLE IF NOT EXISTS auditoria_status_manual (
    id INT AUTO_INCREMENT PRIMARY KEY,
    empresa_id INT NOT NULL,
    tipo_tributo ENUM('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal') NOT NULL,
    status ENUM('nao_aplicavel', 'aplicavel') NOT NULL DEFAULT 'aplicavel',
    motivo TEXT,
    data_marcacao DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    usuario_id INT NOT NULL,
    
    -- Índices
    UNIQUE KEY unique_empresa_tributo (empresa_id, tipo_tributo),
    KEY idx_empresa_id (empresa_id),
    KEY idx_tipo_tributo (tipo_tributo),
    KEY idx_usuario_id (usuario_id),
    
    -- <PERSON><PERSON> estrangeira<PERSON>
    FOREIGN KEY (empresa_id) REFERENCES empresa(id) ON DELETE CASCADE,
    FOREIGN KEY (usuario_id) REFERENCES usuario(id) ON DELETE RESTRICT
);

-- Comentários
ALTER TABLE auditoria_status_manual COMMENT = 'Controle manual de status de auditoria por tributo';
ALTER TABLE auditoria_status_manual MODIFY COLUMN empresa_id INT COMMENT 'ID da empresa';
ALTER TABLE auditoria_status_manual MODIFY COLUMN tipo_tributo ENUM('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal') COMMENT 'Tipo do tributo';
ALTER TABLE auditoria_status_manual MODIFY COLUMN status ENUM('nao_aplicavel', 'aplicavel') COMMENT 'Status manual do tributo';
ALTER TABLE auditoria_status_manual MODIFY COLUMN motivo TEXT COMMENT 'Motivo da marcação manual';
ALTER TABLE auditoria_status_manual MODIFY COLUMN data_marcacao DATETIME COMMENT 'Data da marcação';
ALTER TABLE auditoria_status_manual MODIFY COLUMN usuario_id INT COMMENT 'Usuário que fez a marcação';
