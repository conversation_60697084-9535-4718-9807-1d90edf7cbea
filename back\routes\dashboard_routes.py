from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, Usuario, Empresa, AuditoriaSumario
from sqlalchemy import and_, func, distinct
from datetime import datetime

# Criar blueprint
dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/api/dashboard/estatisticas', methods=['GET'])
@jwt_required()
def obter_estatisticas_dashboard():
    """
    Obtém estatísticas para o dashboard principal
    """
    try:
        # Obter parâmetros de filtro
        year = request.args.get('year', type=int, default=datetime.now().year)
        month = request.args.get('month', type=int, default=datetime.now().month)

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Obter empresas do usuário baseado na hierarquia
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores veem todas as empresas
            empresas_query = Empresa.query
        elif usuario.tipo_usuario == 'escritorio':
            # Usuários do tipo escritório veem empresas do seu escritório
            empresas_query = Empresa.query.filter_by(escritorio_id=usuario.escritorio_id)
        else:
            # Usuários comuns veem apenas empresas permitidas
            empresas_permitidas = usuario.empresas_permitidas or []
            if not empresas_permitidas:
                return jsonify({
                    "success": True,
                    "estatisticas": {
                        "total_empresas": 0,
                        "empresas_auditadas": 0,
                        "empresas_pendentes": 0
                    }
                }), 200
            empresas_query = Empresa.query.filter(Empresa.id.in_(empresas_permitidas))

        # Contar total de empresas
        total_empresas = empresas_query.count()

        # Obter IDs das empresas
        empresa_ids = [e.id for e in empresas_query.all()]

        if not empresa_ids:
            return jsonify({
                "success": True,
                "estatisticas": {
                    "total_empresas": 0,
                    "empresas_auditadas": 0,
                    "empresas_pendentes": 0
                }
            }), 200

        # Verificar quais empresas têm auditoria completa (todos os 6 tributos)
        # Uma empresa é considerada auditada se tem sumários para todos os 6 tributos no ano/mês
        tributos_obrigatorios = ['icms', 'icms_st', 'difal', 'ipi', 'pis', 'cofins']

        # Buscar empresas que têm sumários para todos os tributos no período
        empresas_com_sumarios = db.session.query(
            AuditoriaSumario.empresa_id,
            func.count(distinct(AuditoriaSumario.tipo_tributo)).label('tributos_count')
        ).filter(
            AuditoriaSumario.empresa_id.in_(empresa_ids),
            AuditoriaSumario.ano == year,
            AuditoriaSumario.mes == month,
            AuditoriaSumario.tipo_tributo.in_(tributos_obrigatorios)
        ).group_by(AuditoriaSumario.empresa_id).all()

        # Contar empresas que têm todos os 6 tributos auditados
        empresas_auditadas = len([e for e in empresas_com_sumarios if e.tributos_count == 6])
        empresas_pendentes = total_empresas - empresas_auditadas

        return jsonify({
            "success": True,
            "estatisticas": {
                "total_empresas": total_empresas,
                "empresas_auditadas": empresas_auditadas,
                "empresas_pendentes": empresas_pendentes
            }
        }), 200

    except Exception as e:
        print(f"Erro ao obter estatísticas do dashboard: {str(e)}")
        return jsonify({
            "success": False,
            "message": "Erro interno do servidor"
        }), 500


@dashboard_bp.route('/api/dashboard/empresas', methods=['GET'])
@jwt_required()
def listar_empresas_dashboard():
    """
    Lista empresas para o dashboard com status de auditoria
    """
    try:
        # Obter parâmetros de filtro
        year = request.args.get('year', type=int, default=datetime.now().year)
        month = request.args.get('month', type=int, default=datetime.now().month)

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Obter empresas do usuário baseado na hierarquia
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores veem todas as empresas
            empresas = Empresa.query.all()
        elif usuario.tipo_usuario == 'escritorio':
            # Usuários do tipo escritório veem empresas do seu escritório
            empresas = Empresa.query.filter_by(escritorio_id=usuario.escritorio_id).all()
        else:
            # Usuários comuns veem apenas empresas permitidas
            empresas_permitidas = usuario.empresas_permitidas or []
            if not empresas_permitidas:
                return jsonify({
                    "success": True,
                    "empresas": []
                }), 200
            empresas = Empresa.query.filter(Empresa.id.in_(empresas_permitidas)).all()

        # Preparar lista de empresas com status de auditoria
        empresas_resultado = []
        tributos_obrigatorios = ['icms', 'icms_st', 'difal', 'ipi', 'pis', 'cofins']

        for empresa in empresas:
            # Buscar sumários de auditoria para a empresa no período
            sumarios = AuditoriaSumario.query.filter(
                AuditoriaSumario.empresa_id == empresa.id,
                AuditoriaSumario.ano == year,
                AuditoriaSumario.mes == month,
                AuditoriaSumario.tipo_tributo.in_(tributos_obrigatorios)
            ).all()

            # Criar mapa de tributos auditados
            tributos_auditados = {s.tipo_tributo for s in sumarios}
            tributos_pendentes = set(tributos_obrigatorios) - tributos_auditados

            # Determinar status geral
            if len(tributos_auditados) == 6:
                status_auditoria = 'completa'
            elif len(tributos_auditados) > 0:
                status_auditoria = 'parcial'
            else:
                status_auditoria = 'pendente'

            # Calcular totais de inconsistências
            total_inconsistencias = sum(s.total_inconsistente for s in sumarios)
            total_valor_inconsistente = sum(
                float(s.valor_inconsistente_maior or 0) + float(s.valor_inconsistente_menor or 0)
                for s in sumarios
            )

            empresa_data = {
                'id': empresa.id,
                'razao_social': empresa.razao_social or empresa.nome_fantasia or empresa.nome,
                'cnpj': empresa.cnpj,
                'status_auditoria': status_auditoria,
                'tributos_auditados': list(tributos_auditados),
                'tributos_pendentes': list(tributos_pendentes),
                'total_inconsistencias': total_inconsistencias,
                'total_valor_inconsistente': total_valor_inconsistente,
                'progresso_auditoria': len(tributos_auditados) / 6 * 100  # Percentual de conclusão
            }

            empresas_resultado.append(empresa_data)

        # Ordenar por status (completa, parcial, pendente) e depois por nome
        def sort_key(empresa):
            status_order = {'completa': 0, 'parcial': 1, 'pendente': 2}
            return (status_order.get(empresa['status_auditoria'], 3), empresa['razao_social'])

        empresas_resultado.sort(key=sort_key)

        return jsonify({
            "success": True,
            "empresas": empresas_resultado
        }), 200

    except Exception as e:
        print(f"Erro ao listar empresas do dashboard: {str(e)}")
        return jsonify({
            "success": False,
            "message": "Erro interno do servidor"
        }), 500


@dashboard_bp.route('/api/dashboard/empresa/<int:empresa_id>', methods=['GET'])
@jwt_required()
def obter_dashboard_empresa(empresa_id):
    """
    Obtém dashboard específico de uma empresa com estatísticas por tributo
    """
    try:
        # Obter parâmetros de filtro
        year = request.args.get('year', type=int, default=datetime.now().year)
        month = request.args.get('month', type=int, default=datetime.now().month)

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Verificar se o usuário tem acesso à empresa
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa:
            return jsonify({"message": "Empresa não encontrada"}), 404

        # Verificar permissões para visualizar a empresa
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores podem ver qualquer empresa
            pass
        elif usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id:
            # Usuários do tipo escritório só podem ver empresas do seu próprio escritório
            pass
        elif usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas:
            # Usuários comuns só podem ver empresas permitidas
            pass
        else:
            return jsonify({"message": "Você não tem permissão para visualizar esta empresa"}), 403

        # Buscar sumários de auditoria para todos os tributos
        tributos_obrigatorios = ['icms', 'icms_st', 'difal', 'ipi', 'pis', 'cofins']
        sumarios = AuditoriaSumario.query.filter(
            AuditoriaSumario.empresa_id == empresa_id,
            AuditoriaSumario.ano == year,
            AuditoriaSumario.mes == month,
            AuditoriaSumario.tipo_tributo.in_(tributos_obrigatorios)
        ).all()

        # Criar mapa de sumários por tipo de tributo
        sumarios_map = {s.tipo_tributo: s for s in sumarios}

        # Preparar dados dos cards por tributo
        cards_tributos = []

        for tributo in tributos_obrigatorios:
            sumario = sumarios_map.get(tributo)

            if sumario:
                # Tributo auditado
                card_data = {
                    'tipo_tributo': tributo,
                    'nome_tributo': tributo.upper().replace('_', '-'),
                    'auditado': True,
                    'total_notas': sumario.total_notas,
                    'total_produtos': sumario.total_produtos,
                    'total_conforme': sumario.total_conforme,
                    'total_inconsistente': sumario.total_inconsistente,
                    'valor_total_notas': float(sumario.valor_total_notas or 0),
                    'valor_total_cenarios': float(sumario.valor_total_cenarios or 0),
                    'valor_inconsistente_maior': float(sumario.valor_inconsistente_maior or 0),
                    'valor_inconsistente_menor': float(sumario.valor_inconsistente_menor or 0),
                    'notas_conformes': sumario.notas_conformes,
                    'notas_inconsistentes': sumario.notas_inconsistentes,
                    'data_atualizacao': sumario.data_atualizacao.isoformat() if sumario.data_atualizacao else None
                }
            else:
                # Tributo não auditado
                card_data = {
                    'tipo_tributo': tributo,
                    'nome_tributo': tributo.upper().replace('_', '-'),
                    'auditado': False,
                    'total_notas': 0,
                    'total_produtos': 0,
                    'total_conforme': 0,
                    'total_inconsistente': 0,
                    'valor_total_notas': 0,
                    'valor_total_cenarios': 0,
                    'valor_inconsistente_maior': 0,
                    'valor_inconsistente_menor': 0,
                    'notas_conformes': 0,
                    'notas_inconsistentes': 0,
                    'data_atualizacao': None
                }

            cards_tributos.append(card_data)

        # Calcular estatísticas gerais da empresa
        total_geral = {
            'total_notas': sum(card['total_notas'] for card in cards_tributos),
            'total_produtos': sum(card['total_produtos'] for card in cards_tributos),
            'total_conforme': sum(card['total_conforme'] for card in cards_tributos),
            'total_inconsistente': sum(card['total_inconsistente'] for card in cards_tributos),
            'valor_total_inconsistente': sum(
                card['valor_inconsistente_maior'] + card['valor_inconsistente_menor']
                for card in cards_tributos
            ),
            'tributos_auditados': len([card for card in cards_tributos if card['auditado']]),
            'tributos_pendentes': len([card for card in cards_tributos if not card['auditado']])
        }

        return jsonify({
            "success": True,
            "empresa": {
                "id": empresa.id,
                "razao_social": empresa.razao_social or empresa.nome_fantasia or empresa.nome,
                "cnpj": empresa.cnpj
            },
            "periodo": {
                "ano": year,
                "mes": month
            },
            "cards_tributos": cards_tributos,
            "total_geral": total_geral
        }), 200

    except Exception as e:
        print(f"Erro ao obter dashboard da empresa: {str(e)}")
        return jsonify({
            "success": False,
            "message": "Erro interno do servidor"
        }), 500
