/*
 * Dashboard CSS - Auditoria Fiscal
 * Estilos específicos para o dashboard e componentes relacionados
 */

/* === LAYOUT PRINCIPAL MODERNO === */
.dashboard-container {
  display: grid;
  grid-template-areas:
    'header header'
    'sidebar content';
  grid-template-columns: var(--sidebar-width) 1fr;
  grid-template-rows: var(--header-height) 1fr;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  transition: grid-template-columns var(--transition-normal) ease-out;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
}

/* === HEADER MODERNO === */
.dashboard-header {
  grid-area: header;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-xl);
  background: linear-gradient(135deg, white 0%, var(--gray-50) 100%);
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-lg);
  z-index: 100;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Elementos do header */
.header-left {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.toggle-sidebar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, var(--primary-50) 0%, white 100%);
  border: 1px solid var(--primary-200);
  border-radius: var(--radius-xl);
  color: var(--primary-600);
  font-size: 1.125rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.toggle-sidebar-btn:hover {
  background: var(--primary-gradient);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-primary);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.header-logo {
  height: 48px;
  width: auto;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: transform var(--transition-fast);
}

.header-logo:hover {
  transform: scale(1.05);
}

.logo-text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-800);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.header-filters {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-sm);
  background: linear-gradient(135deg, white 0%, var(--gray-50) 100%);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

.company-selector,
.year-selector,
.month-selector {
  min-width: 160px;
}

.company-selector select,
.year-selector select,
.month-selector select {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--space-sm) var(--space-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  background-color: white;
  color: var(--gray-700);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.company-selector select:focus,
.year-selector select:focus,
.month-selector select:focus {
  border-color: var(--primary-400);
  box-shadow: var(--shadow-md), 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.theme-toggle {
  display: flex;
  align-items: center;
}

.theme-toggle .btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, var(--warning-50) 0%, white 100%);
  border: 1px solid var(--warning-200);
  border-radius: var(--radius-xl);
  color: var(--warning-600);
  font-size: 1.125rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.theme-toggle .btn:hover {
  background: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-500) 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-warning);
}

.theme-toggle .btn i {
  transition: transform var(--transition-fast);
}

.user-profile {
  position: relative;
}

.user-profile .btn {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-xl);
  color: var(--gray-700);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.user-profile .btn:hover {
  background: linear-gradient(135deg, var(--primary-50) 0%, white 100%);
  border-color: var(--primary-200);
  color: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.user-profile .btn i {
  font-size: 1.25rem;
  color: var(--gray-500);
  transition: color var(--transition-fast);
}

.user-profile .btn:hover i {
  color: var(--primary-600);
}

/* Tema escuro para header */
body.dark-theme .dashboard-header {
  background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-850) 100%);
  border-bottom-color: var(--gray-700);
}

body.dark-theme .toggle-sidebar-btn {
  background: linear-gradient(135deg, var(--primary-800) 0%, var(--gray-700) 100%);
  border-color: var(--primary-700);
  color: var(--primary-400);
}

body.dark-theme .toggle-sidebar-btn:hover {
  background: var(--primary-gradient);
  color: white;
}

body.dark-theme .logo-text {
  color: var(--gray-200);
}

body.dark-theme .header-filters {
  background: linear-gradient(135deg, var(--gray-700) 0%, var(--gray-800) 100%);
  border-color: var(--gray-600);
}

body.dark-theme .company-selector select,
body.dark-theme .year-selector select,
body.dark-theme .month-selector select {
  background-color: var(--gray-800);
  border-color: var(--gray-600);
  color: var(--gray-200);
}

body.dark-theme .theme-toggle .btn {
  background: linear-gradient(135deg, var(--warning-800) 0%, var(--gray-700) 100%);
  border-color: var(--warning-700);
  color: var(--warning-400);
}

body.dark-theme .theme-toggle .btn:hover {
  background: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-500) 100%);
  color: white;
}

body.dark-theme .user-profile .btn {
  background: linear-gradient(135deg, var(--gray-700) 0%, var(--gray-800) 100%);
  border-color: var(--gray-600);
  color: var(--gray-300);
}

body.dark-theme .user-profile .btn:hover {
  background: linear-gradient(135deg, var(--primary-800) 0%, var(--gray-700) 100%);
  border-color: var(--primary-600);
  color: var(--primary-300);
}

/* === SIDEBAR MODERNA === */
.dashboard-sidebar {
  grid-area: sidebar;
  background: linear-gradient(180deg, white 0%, var(--gray-50) 100%);
  border-right: 1px solid var(--gray-200);
  height: calc(100vh - var(--header-height));
  overflow-y: auto;
  overflow-x: hidden;
  transition: width var(--transition-normal);
  z-index: 90;
  box-shadow: var(--shadow-lg);
}

.sidebar-nav {
  padding: var(--space-xl) 0;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.nav-item {
  margin: 0 var(--space-md) var(--space-xs) var(--space-md);
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  color: var(--gray-600);
  text-decoration: none;
  border-radius: var(--radius-xl);
  transition: all var(--transition-fast);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: var(--primary-gradient);
  transition: width var(--transition-fast);
  z-index: -1;
}

.nav-link:hover {
  color: var(--primary-700);
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}

.nav-link:hover::before {
  width: 4px;
}

.nav-item.active .nav-link {
  color: var(--primary-700);
  background: linear-gradient(135deg, var(--primary-100) 0%, var(--primary-50) 100%);
  box-shadow: var(--shadow-primary);
  font-weight: var(--font-weight-semibold);
}

.nav-item.active .nav-link::before {
  width: 4px;
}

.nav-link i {
  font-size: 1.25rem;
  margin-right: var(--space-md);
  width: 1.5rem;
  text-align: center;
  transition: transform var(--transition-fast);
}

.nav-link:hover i {
  transform: scale(1.1);
}

.nav-link span {
  transition: transform var(--transition-fast);
}

.nav-link:hover span {
  transform: translateX(2px);
}

/* === DROPDOWN MODERNO NA SIDEBAR === */
.nav-dropdown .dropdown-icon {
  margin-left: auto;
  transition: transform var(--transition-normal);
  font-size: 0.875rem;
  color: var(--gray-400);
}

.nav-dropdown.open .dropdown-icon {
  transform: rotate(180deg);
  color: var(--primary-600);
}

.nav-dropdown-menu {
  list-style: none;
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal) ease-out, opacity var(--transition-normal) ease-out;
  opacity: 0;
  background: linear-gradient(135deg, var(--gray-25) 0%, var(--gray-50) 100%);
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
}

.nav-dropdown.open .nav-dropdown-menu {
  max-height: 500px;
  opacity: 1;
  transition: max-height var(--transition-slow) ease-in, opacity var(--transition-slow) ease-in;
}

.nav-dropdown-item {
  margin: 0 var(--space-sm);
}

.nav-dropdown-link {
  display: flex;
  align-items: center;
  padding: var(--space-sm) var(--space-md);
  color: var(--gray-600);
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  transform: translateX(-10px);
  opacity: 0;
  position: relative;
  margin-left: var(--space-lg);
}

.nav-dropdown-link::before {
  content: '';
  position: absolute;
  left: -var(--space-md);
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background-color: var(--gray-300);
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.nav-dropdown.open .nav-dropdown-link {
  transform: translateX(0);
  opacity: 1;
  transition: all var(--transition-fast);
}

.nav-dropdown.open .nav-dropdown-item:nth-child(1) .nav-dropdown-link {
  transition-delay: 0.05s;
}

.nav-dropdown.open .nav-dropdown-item:nth-child(2) .nav-dropdown-link {
  transition-delay: 0.1s;
}

.nav-dropdown.open .nav-dropdown-item:nth-child(3) .nav-dropdown-link {
  transition-delay: 0.15s;
}

.nav-dropdown-link:hover {
  color: var(--primary-700);
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
  transform: translateX(2px);
}

.nav-dropdown-link:hover::before {
  background-color: var(--primary-500);
  transform: translateY(-50%) scale(1.2);
}

.nav-dropdown-item.active .nav-dropdown-link {
  color: var(--primary-700);
  /* background: linear-gradient(135deg, var(--primary-100) 0%, var(--primary-50) 100%); */
  font-weight: var(--font-weight-semibold);
}

.nav-dropdown-item.active .nav-dropdown-link::before {
  background-color: var(--primary-600);
  transform: translateY(-50%) scale(1.3);
}

/* Conteúdo principal */
.dashboard-content {
  grid-area: content;
  padding: 1.5rem;
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100vh - var(--header-height));
  width: 100%;
}

.page-section {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.page-section.active {
  display: block;
  animation: slideInUp 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animações para os cards */
.dashboard-card, .fiscal-card {
  animation: fadeInScale 0.5s ease-out;
  animation-fill-mode: both;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card:hover, .fiscal-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.dashboard-cards .col-md-6:nth-child(1) .dashboard-card,
.row .col-md-4:nth-child(1) .fiscal-card {
  animation-delay: 0.1s;
}

.dashboard-cards .col-md-6:nth-child(2) .dashboard-card,
.row .col-md-4:nth-child(2) .fiscal-card {
  animation-delay: 0.2s;
}

.dashboard-cards .col-md-6:nth-child(3) .dashboard-card,
.row .col-md-4:nth-child(3) .fiscal-card {
  animation-delay: 0.3s;
}

.dashboard-cards .col-md-6:nth-child(4) .dashboard-card {
  animation-delay: 0.4s;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* === CARDS FISCAIS MODERNOS === */
.card-link-wrapper {
  display: block;
  text-decoration: none;
  color: inherit;
  margin-bottom: var(--space-2xl);
  transition: all var(--transition-normal);
}

.card-link-wrapper:hover {
  transform: translateY(-12px);
  text-decoration: none;
  color: inherit;
}

.fiscal-card {
  border: none;
  border-radius: var(--radius-3xl);
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  height: 100%;
  background: linear-gradient(145deg, white 0%, var(--gray-50) 100%);
  transition: all var(--transition-normal);
  position: relative;
  border: 1px solid var(--gray-200);
}

.fiscal-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.card-link-wrapper:hover .fiscal-card {
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-200);
  transform: scale(1.02);
}

.card-link-wrapper:hover .fiscal-card::before {
  opacity: 1;
}

.fiscal-card .card-body {
  padding: var(--space-2xl);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 200px;
  position: relative;
}

.fiscal-card .card-icon {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow: hidden;
}

.fiscal-card .card-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: rotate(45deg);
  transition: transform var(--transition-slow);
}

.card-link-wrapper:hover .fiscal-card .card-icon::before {
  transform: rotate(45deg) translate(100%, 100%);
}

.fiscal-card .card-icon i {
  font-size: var(--font-size-3xl);
  color: white;
  z-index: 1;
  transition: transform var(--transition-fast);
}

.card-link-wrapper:hover .fiscal-card .card-icon i {
  transform: scale(1.1);
}

.fiscal-card .card-info {
  width: 100%;
}

.fiscal-card .card-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: 0;
  color: var(--gray-800);
  letter-spacing: 0.025em;
  transition: color var(--transition-fast);
}

.card-link-wrapper:hover .fiscal-card .card-title {
  color: var(--primary-700);
}

/* === CORES MODERNAS PARA ÍCONES === */
.bg-primary {
  background: var(--primary-gradient);
  box-shadow: var(--shadow-primary);
}

.bg-success {
  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);
  box-shadow: var(--shadow-success);
}

.bg-info {
  background: linear-gradient(135deg, var(--info-600) 0%, var(--info-500) 100%);
  box-shadow: 0 10px 15px -3px rgba(6, 182, 212, 0.1), 0 4px 6px -2px rgba(6, 182, 212, 0.05);
}

.bg-warning {
  background: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-500) 100%);
  box-shadow: var(--shadow-warning);
}

.bg-danger {
  background: linear-gradient(135deg, var(--danger-600) 0%, var(--danger-500) 100%);
  box-shadow: var(--shadow-danger);
}

.bg-secondary {
  background: linear-gradient(135deg, var(--gray-600) 0%, var(--gray-500) 100%);
  box-shadow: 0 10px 15px -3px rgba(100, 116, 139, 0.1), 0 4px 6px -2px rgba(100, 116, 139, 0.05);
}

/* Estilos para as tabs */
.tab-content {
  padding: 1.5rem 0;
}

.tab-pane {
  display: none;
  animation: fadeIn 0.3s ease-out;
}

.tab-pane.active {
  display: block;
}

/* Estilo melhorado para as tabs */
.nav-tabs .nav-link {
  color: var(--gray-600);
  border-radius: 0.375rem 0.375rem 0 0;
  padding: 0.75rem 1.25rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  margin-right: 0.25rem;
}

.nav-tabs .nav-link:hover {
  color: var(--primary-color);
  background-color: rgba(59, 130, 246, 0.05);
  border-color: transparent;
}

.nav-tabs .nav-link.active {
  color: var(--primary-color);
  background-color: rgba(59, 130, 246, 0.1);
  border-color: var(--gray-200);
  border-bottom-color: transparent;
  position: relative;
}

.nav-tabs .nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background-color: var(--primary-color);
}

/* Tema escuro para as tabs */
body.dark-theme .nav-tabs .nav-link {
  color: var(--gray-400);
}

body.dark-theme .nav-tabs .nav-link:hover {
  color: var(--primary-light);
  background-color: rgba(59, 130, 246, 0.1);
}

body.dark-theme .nav-tabs .nav-link.active {
  color: var(--primary-light);
  background-color: rgba(59, 130, 246, 0.2);
  border-color: var(--gray-700);
  border-bottom-color: transparent;
}

.btn-group .btn-outline-primary {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-group .btn-outline-primary:hover,
.btn-group .btn-outline-primary.active {
  background-color: var(--primary-color);
  color: white;
}

/* Estilos para o header das seções */
.header-actions {
  display: flex;
  align-items: center;
}

/* === SEÇÕES E HEADERS === */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-2xl);
  padding-bottom: var(--space-lg);
  border-bottom: 2px solid var(--gray-200);
}

.section-header h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-800);
  margin: 0;
  display: flex;
  align-items: center;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-header h2 i {
  margin-right: var(--space-lg);
  color: var(--primary-600);
  font-size: var(--font-size-2xl);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* === CARDS DO DASHBOARD MODERNOS === */
.dashboard-cards {
  margin-bottom: var(--space-2xl);
}

.dashboard-card {
  height: 100%;
  background: linear-gradient(145deg, white 0%, var(--gray-50) 100%);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-3xl);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  overflow: hidden;
  position: relative;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.dashboard-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-200);
}

.dashboard-card:hover::before {
  opacity: 1;
}

.dashboard-card .card-body {
  display: flex;
  align-items: center;
  padding: var(--space-2xl);
  position: relative;
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: var(--radius-2xl);
  color: white;
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.card-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: transform var(--transition-slow);
}

.dashboard-card:hover .card-icon::before {
  transform: rotate(45deg) translate(100%, 100%);
}

.card-icon i {
  font-size: var(--font-size-2xl);
  z-index: 1;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-600);
  margin-bottom: var(--space-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.card-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-800);
  margin: 0;
  line-height: var(--line-height-tight);
}

.card-footer {
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  background: linear-gradient(135deg, var(--gray-25) 0%, var(--gray-50) 100%);
  border-top: 1px solid var(--gray-200);
  font-weight: var(--font-weight-medium);
}

/* === SIDEBAR COLAPSADA === */
.dashboard-container.sidebar-collapsed {
  grid-template-columns: var(--sidebar-collapsed-width) 1fr;
}

.dashboard-container.sidebar-collapsed .dashboard-sidebar {
  width: var(--sidebar-collapsed-width);
}

/* Ajuste do conteúdo quando a sidebar está colapsada */
.dashboard-container.sidebar-collapsed .dashboard-content {
  width: calc(100vw - var(--sidebar-collapsed-width));
  margin-left: 0;
}

.dashboard-content {
  grid-area: content;
  width: 100%;
  margin-left: 0;
  transition: all var(--transition-normal);
  overflow-x: auto;
}

/* Estilos para os filtros de colunas na tabela */
.filters th {
  padding: 0.5rem;
}

.filters .column-filter {
  width: 100%;
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--gray-300);
}

body.dark-theme .filters .column-filter {
  background-color: var(--gray-700);
  border-color: var(--gray-600);
  color: var(--gray-300);
}

/* Ajuste para tabelas responsivas */
.table-responsive {
  overflow-x: auto;
  width: 100%;
}

/* Estilos adicionais para a tabela de cenários */
#cenario-novo-table,
#cenario-producao-table,
#cenario-inconsistente-table,
#cenario-conforme-table {
  width: 100% !important;
}

/* Garantir que as células da tabela tenham um tamanho mínimo */
.table th, .table td {
  white-space: nowrap;
}


/* Garantir que os filtros de coluna tenham o mesmo tamanho que as colunas */
.filters th {
  min-width: 100px;
}

/* === SCROLLBAR DUPLA PARA TABELAS === */
.table-container-with-dual-scroll {
  position: relative;
}

.table-top-scrollbar {
  overflow-x: auto;
  overflow-y: hidden;
  height: 20px;
  margin-bottom: 10px;
  border: 1px solid var(--gray-300);
  border-radius: 4px;
  background-color: var(--gray-50);
}

.table-top-scrollbar-content {
  height: 1px;
  /* A largura será definida dinamicamente via JavaScript */
}

/* Tema escuro para scrollbar superior */
body.dark-theme .table-top-scrollbar {
  border-color: var(--gray-600);
  background-color: var(--gray-700);
}

/* Estilizar as scrollbars para melhor aparência */
.table-top-scrollbar::-webkit-scrollbar,
.table-responsive::-webkit-scrollbar {
  height: 12px;
}

.table-top-scrollbar::-webkit-scrollbar-track,
.table-responsive::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 6px;
}

.table-top-scrollbar::-webkit-scrollbar-thumb,
.table-responsive::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: 6px;
  border: 2px solid var(--gray-100);
}

.table-top-scrollbar::-webkit-scrollbar-thumb:hover,
.table-responsive::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* Tema escuro para scrollbars */
body.dark-theme .table-top-scrollbar::-webkit-scrollbar-track,
body.dark-theme .table-responsive::-webkit-scrollbar-track {
  background: var(--gray-700);
}

body.dark-theme .table-top-scrollbar::-webkit-scrollbar-thumb,
body.dark-theme .table-responsive::-webkit-scrollbar-thumb {
  background: var(--gray-500);
  border-color: var(--gray-700);
}

body.dark-theme .table-top-scrollbar::-webkit-scrollbar-thumb:hover,
body.dark-theme .table-responsive::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* === ESTILOS PARA SIDEBAR COLAPSADA === */
.dashboard-container.sidebar-collapsed .nav-link span,
.dashboard-container.sidebar-collapsed .dropdown-icon {
  display: none;
}

.dashboard-container.sidebar-collapsed .nav-link i:not(.dropdown-icon) {
  margin-right: 0;
  font-size: 1.25rem;
}

.dashboard-container.sidebar-collapsed .nav-link {
  justify-content: center;
  padding: var(--space-md);
}

/* Comportamento especial para dropdowns na sidebar recolhida */
.dashboard-container.sidebar-collapsed .nav-dropdown-menu {
  position: fixed;
  left: var(--sidebar-collapsed-width);
  top: auto;
  width: 220px;
  z-index: 1000;
  background-color: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-height: 0;
  opacity: 0;
  pointer-events: none;
  transition: max-height var(--transition-fast) ease-out, opacity var(--transition-fast) ease-out;
  margin-left: 5px;
}

.dashboard-container.sidebar-collapsed .nav-dropdown.open .nav-dropdown-menu {
  max-height: 300px;
  opacity: 1;
  pointer-events: auto;
}

/* Área expandida para hover na sidebar colapsada */
.dashboard-container.sidebar-collapsed .nav-dropdown {
  position: relative;
}

.dashboard-container.sidebar-collapsed .nav-dropdown::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: -225px;
  bottom: 0;
  z-index: 999;
  pointer-events: none;
}

.h1 {
  font-family:'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-800);

}

.dashboard-container.sidebar-collapsed .nav-dropdown.open::after {
  pointer-events: auto;
}

.dashboard-container.sidebar-collapsed .nav-dropdown-link {
  padding: var(--space-md) var(--space-lg);
}

/* Animação para a sidebar */
.dashboard-sidebar {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Tema escuro */
body.dark-theme .dashboard-header {
  background-color: var(--gray-800);
  border-color: var(--gray-700);
}

body.dark-theme .logo-text {
  color: var(--gray-200);
}

body.dark-theme .toggle-sidebar-btn {
  color: var(--gray-400);
}

body.dark-theme .user-profile .btn {
  color: var(--gray-300);
}

body.dark-theme .dashboard-sidebar {
  background-color: var(--gray-800);
  border-color: var(--gray-700);
}

body.dark-theme .nav-link {
  color: var(--gray-400);
}

body.dark-theme .nav-link:hover {
  color: var(--primary-light);
  background-color: var(--gray-700);
}

body.dark-theme .nav-item.active .nav-link {
  color: var(--primary-light);
  background-color: rgba(59, 130, 246, 0.2);
}

/* === TEMA ESCURO PARA NOVOS COMPONENTES === */
body.dark-theme .dashboard-container {
  background: #141414;
}

body.dark-theme .dashboard-header {
  background: #1e293b;
  border-bottom-color: #475569;
}

body.dark-theme .dashboard-sidebar {
  background: #1e293b;
  border-right-color: #475569;
}

body.dark-theme .toggle-sidebar-btn {
  background: #3a3e45;
  border-color: #878d95;
  color: #94a3b8;
}

body.dark-theme .toggle-sidebar-btn:hover {
  background: var(--primary-gradient);
  color: white;
}

body.dark-theme .logo-text {
  color: #e2e8f0;
}

body.dark-theme .header-filters {
  background: #3a3e45;
  border-color: #3a3e45;
}

body.dark-theme .company-selector select,
body.dark-theme .year-selector select,
body.dark-theme .month-selector select {
  background-color: #3a3e45;
  border-color: #878d95;
  color: #e2e8f0;
}

body.dark-theme .theme-toggle .btn {
  background: #3a3e45;
  border-color: #878d95;
  color: #94a3b8;
}

body.dark-theme .theme-toggle .btn:hover {
  background: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-500) 100%);
  color: white;
}

body.dark-theme .user-profile .btn {
  background: #3a3e45;
  border-color: #878d95;
  color: #cbd5e1;
}

body.dark-theme .user-profile .btn:hover {
  background: #475569;
  border-color: #878d95;
  color: #e2e8f0;
}

body.dark-theme .nav-link {
  color: #94a3b8;
}

body.dark-theme .nav-link:hover {
  color: var(--primary-400);
  background: #334155;
}

body.dark-theme .nav-item.active .nav-link {
  color: var(--primary-400);
  background: #334155;
}

body.dark-theme .nav-dropdown-menu {
  background: #33363a;
  border-color: #878d95;
}

body.dark-theme .nav-dropdown-link {
  color: #94a3b8;
}

body.dark-theme .nav-dropdown-link:hover {
  color: var(--primary-400);
  background: #475569;
}

body.dark-theme .section-header {
  border-bottom-color: #475569;
}

body.dark-theme .section-header h2 {
  color: #e2e8f0;
}

body.dark-theme .dashboard-card {
  background: #1e293b;
  border-color: #475569;
}

body.dark-theme .dashboard-card:hover {
  border-color: var(--primary-600);
}

body.dark-theme .card-title {
  color: #94a3b8;
}

body.dark-theme .card-value {
  color: #e2e8f0;
}

body.dark-theme .card-footer {
  background: #334155;
  border-top-color: #475569;
}

body.dark-theme .fiscal-card {
  background: #1e293b;
  border-color: #475569;
}

body.dark-theme .fiscal-card .card-title {
  color: #e2e8f0;
}

body.dark-theme .card-link-wrapper:hover .fiscal-card .card-title {
  color: var(--primary-400);
}

/* Tema escuro para dropdowns colapsados */
body.dark-theme .dashboard-container.sidebar-collapsed .nav-dropdown-menu {
  background-color: #1e293b;
  border-color: #475569;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Responsividade */
@media (max-width: 992px) {
  .dashboard-container {
    grid-template-columns: 0 1fr;
  }

  .dashboard-sidebar {
    position: fixed;
    left: 0;
    top: var(--header-height);
    width: var(--sidebar-width);
    transform: translateX(-100%);
    transition: transform var(--transition-speed);
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  }

  .sidebar-visible .dashboard-sidebar {
    transform: translateX(0);
  }

  .header-filters {
    display: none;
  }
}

/* === ESTILOS PARA NOVO DASHBOARD === */

/* Estilos para cards de empresa */
.empresa-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid var(--border-color);
}

.empresa-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.empresa-card .badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

.empresa-card .progress {
  background-color: var(--bg-secondary);
}

.empresa-card .progress-bar {
  transition: width 0.3s ease;
}

/* Estilos para cards de tributo */
.tributo-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 2px solid;
  cursor: pointer;
}

.tributo-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.tributo-card.border-success {
  border-color: var(--success-color) !important;
}

.tributo-card.border-warning {
  border-color: var(--warning-color) !important;
}

/* Seção de empresas */
.empresas-section .section-header h4 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.empresas-section .section-header p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

/* Breadcrumb personalizado */
.breadcrumb-container {
  background: transparent;
  padding: 0;
}

.breadcrumb {
  background: var(--bg-secondary);
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  margin-bottom: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  color: var(--text-secondary);
}

.breadcrumb-item a {
  color: var(--primary-color);
  text-decoration: none;
}

.breadcrumb-item a:hover {
  color: var(--primary-hover);
}

.breadcrumb-item.active {
  color: var(--text-primary);
}

/* Cards de resumo geral */
.resumo-geral .card {
  border: none;
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.resumo-geral .card h5 {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.resumo-geral .card h2 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

/* Dark mode adjustments */
[data-theme="dark"] .empresa-card,
body.dark-theme .empresa-card {
  background-color: var(--gray-800);
  border-color: var(--gray-600);
}

[data-theme="dark"] .empresa-card:hover,
body.dark-theme .empresa-card:hover {
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .tributo-card:hover,
body.dark-theme .tributo-card:hover {
  box-shadow: 0 6px 20px rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .breadcrumb,
body.dark-theme .breadcrumb {
  background: var(--gray-700);
}

/* Responsividade para novos elementos */
@media (max-width: 768px) {
  .empresa-card .row.text-center {
    font-size: 0.85rem;
  }

  .tributo-card .card-body {
    padding: 1rem;
  }

  .resumo-geral .card h2 {
    font-size: 1.5rem;
  }
}
